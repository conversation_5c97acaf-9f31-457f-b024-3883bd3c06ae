package com.gamegards.gaming27.Utils;

import android.app.Application;
import android.content.Context;
import android.util.Log;
import androidx.fragment.app.FragmentActivity;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;

import com.reown.android_core.Core;
import com.reown.android_core.CoreClient;
import com.reown.appkit.AppKit;
import com.reown.appkit.Modal;
import com.reown.appkit.presets.AppKitChainsPresets;
import com.reown.appkit.ui.AppKitComponentKt;

import java.util.Arrays;
import java.util.List;

/**
 * WalletConnectionManager handles all wallet connection operations using Reown AppKit
 * Provides real wallet connections to TrustWallet and other supported wallets
 */
public class WalletConnectionManager {
    private static final String TAG = "WalletConnectionManager";
    private static WalletConnectionManager instance;
    private static boolean isInitialized = false;
    
    // Reown Project ID - Get this from https://cloud.reown.com/
    // TODO: Replace with your actual project ID from Reown Cloud Dashboard
    private static final String PROJECT_ID = "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"; // Placeholder - replace with real ID
    
    private WalletConnectionManager() {}
    
    public static synchronized WalletConnectionManager getInstance() {
        if (instance == null) {
            instance = new WalletConnectionManager();
        }
        return instance;
    }
    
    /**
     * Initialize the Reown AppKit SDK
     * Call this once in your Application class or main activity
     */
    public void initialize(Application application) {
        if (isInitialized) {
            Log.d(TAG, "WalletConnectionManager already initialized");
            return;
        }
        
        try {
            Log.d(TAG, "Initializing Reown AppKit SDK...");
            
            // Configure connection type
            Core.Model.ConnectionType connectionType = Core.Model.ConnectionType.AUTOMATIC;
            
            // Set up app metadata
            Core.Model.AppMetaData appMetaData = new Core.Model.AppMetaData(
                "Gaming27", // App name
                "Gaming27 - Real Money Gaming Platform", // Description
                "https://gaming27.com", // URL
                Arrays.asList("https://gaming27.com/icon.png"), // Icons
                "gaming27://wallet" // Redirect URI
            );
            
            // Initialize Core Client
            CoreClient.initialize(
                PROJECT_ID,
                connectionType,
                application,
                appMetaData,
                null // relay server URL (null for default)
            );
            
            // Initialize AppKit
            AppKit.initialize(
                new Modal.Params.Init(CoreClient.INSTANCE),
                () -> {
                    Log.d(TAG, "AppKit initialized successfully");
                    setupChains();
                    isInitialized = true;
                    return null;
                },
                error -> {
                    Log.e(TAG, "Failed to initialize AppKit: " + error.getThrowable().getMessage());
                    return null;
                }
            );
            
        } catch (Exception e) {
            Log.e(TAG, "Error initializing WalletConnectionManager: " + e.getMessage(), e);
        }
    }
    
    /**
     * Set up supported blockchain chains
     */
    private void setupChains() {
        try {
            // Set up Ethereum mainnet and common testnets
            List<Object> chains = Arrays.asList(
                AppKitChainsPresets.INSTANCE.getEthChains().get("1"), // Ethereum Mainnet
                AppKitChainsPresets.INSTANCE.getEthChains().get("11155111"), // Sepolia Testnet
                AppKitChainsPresets.INSTANCE.getEthChains().get("137"), // Polygon Mainnet
                AppKitChainsPresets.INSTANCE.getEthChains().get("56") // BSC Mainnet
            );
            
            AppKit.setChains(chains);
            Log.d(TAG, "Blockchain chains configured successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error setting up chains: " + e.getMessage(), e);
        }
    }
    
    /**
     * Open the wallet connection modal
     * This will show the AppKit modal with available wallets including TrustWallet
     */
    public void openWalletConnection(FragmentActivity activity) {
        if (!isInitialized) {
            Log.e(TAG, "WalletConnectionManager not initialized. Call initialize() first.");
            return;
        }
        
        try {
            Log.d(TAG, "Opening wallet connection modal...");
            
            // Find NavController - this assumes you have navigation set up
            // If not using Navigation Component, you'll need to implement modal differently
            NavController navController = Navigation.findNavController(activity, android.R.id.content);
            
            // Open AppKit modal
            AppKitComponentKt.openAppKit(
                navController,
                true, // shouldOpenChooseNetwork
                error -> {
                    Log.e(TAG, "Error opening wallet modal: " + error.getThrowable().getMessage());
                    return null;
                }
            );
            
        } catch (Exception e) {
            Log.e(TAG, "Error opening wallet connection: " + e.getMessage(), e);
            // Fallback: show a simple dialog explaining wallet connection
            showWalletConnectionFallback(activity);
        }
    }
    
    /**
     * Fallback method if navigation-based modal fails
     */
    private void showWalletConnectionFallback(Context context) {
        // Simple fallback implementation
        Log.d(TAG, "Using fallback wallet connection method");
        // You can implement a simple dialog here or use your existing dialog system
    }
    
    /**
     * Check if wallet is currently connected
     */
    public boolean isWalletConnected() {
        try {
            // Check AppKit connection status
            return AppKit.getSelectedChain() != null;
        } catch (Exception e) {
            Log.e(TAG, "Error checking wallet connection status: " + e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * Get connected wallet address
     */
    public String getConnectedWalletAddress() {
        try {
            if (isWalletConnected()) {
                // Get the connected account address
                return AppKit.getAccount() != null ? AppKit.getAccount().getAddress() : null;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting wallet address: " + e.getMessage(), e);
        }
        return null;
    }
    
    /**
     * Disconnect the current wallet
     */
    public void disconnectWallet() {
        try {
            if (isWalletConnected()) {
                AppKit.disconnect();
                Log.d(TAG, "Wallet disconnected successfully");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error disconnecting wallet: " + e.getMessage(), e);
        }
    }
    
    /**
     * Get the current network/chain name
     */
    public String getConnectedNetworkName() {
        try {
            if (isWalletConnected()) {
                return AppKit.getSelectedChain() != null ? AppKit.getSelectedChain().getName() : "Unknown";
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting network name: " + e.getMessage(), e);
        }
        return "Not Connected";
    }
    
    /**
     * Interface for wallet connection callbacks
     */
    public interface WalletConnectionCallback {
        void onWalletConnected(String address, String networkName);
        void onWalletDisconnected();
        void onConnectionError(String error);
    }
}

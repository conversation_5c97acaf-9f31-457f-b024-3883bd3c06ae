apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'

android {
    compileSdk 35

    defaultConfig {
        applicationId "com.gamegards.gaming27"
        minSdkVersion 23
        targetSdk 35
        versionCode 1
        versionName "1.2"
        multiDexEnabled true

        archivesBaseName = "gaming27games_" + versionCode
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = '11'
    }

    buildFeatures {
        compose true
    }

    composeOptions {
        kotlinCompilerExtensionVersion = '1.5.4'
    }
    signingConfigs {
        debug {
            storeFile file("debug.keystore")
            storePassword "android"
            keyAlias "androiddebugkey"
            keyPassword "android"
        }
        release {
            storeFile file("C:\\Users\\<USER>\\Desktop\\akash\\AndroidStudioProjects\\play24\\secureplay999.jks")
            storePassword "123456"
            keyAlias "key9"
            keyPassword "123456"
        }
    }

    buildTypes {
        debug {
            signingConfig signingConfigs.release
        }
        release {
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
            // debuggable false
            // jniDebuggable false
            // renderscriptDebuggable false
            // pseudoLocalesEnabled false
            // zipAlignEnabled true
        }
    }

    repositories {
        jcenter()
//        maven {
//            url "https://mint.splunk.com/gradle/"
//        }
        maven {
            url "https://jitpack.io"
        }
        mavenCentral()

    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

//    buildFeatures{
//        dataBinding true;
//    }


    android {
        lintOptions {
            checkReleaseBuilds false
            // Or, if you prefer, you can continue to check for errors in release builds,
            // but continue the build even when errors are found:
            abortOnError false
        }
    }
    namespace 'com.gamegards.gaming27'
   /* viewBinding {
        enabled = true
    }*/

    packaging {
        resources {
            excludes += ['META-INF/versions/9/OSGI-INF/MANIFEST.MF']
            excludes += ['META-INF/DEPENDENCIES']
            excludes += ['META-INF/LICENSE']
            excludes += ['META-INF/LICENSE.txt']
            excludes += ['META-INF/NOTICE']
            excludes += ['META-INF/NOTICE.txt']
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.activity:activity:1.9.3'
    implementation 'androidx.navigation:navigation-runtime-android:2.9.0'
    // implementation 'androidx.activity:activity:1.8.0'
    testImplementation 'junit:junit:4.13.2'
    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.2.0'
    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'

    implementation project(path: ':ludogame')

    // Kotlin support
    implementation "org.jetbrains.kotlin:kotlin-stdlib:1.9.20"

    // Reown AppKit for REAL Wallet Integration (Official Implementation)
    implementation platform('com.reown:android-bom:1.4.1')
    implementation 'com.reown:android-core'
    implementation 'com.reown:appkit'

    // Required for AppKit (Compose dependencies)
    implementation 'androidx.compose.ui:ui:1.5.4'
    implementation 'androidx.compose.ui:ui-tooling-preview:1.5.4'
    implementation 'androidx.compose.material:material:1.5.4'
    implementation 'androidx.activity:activity-compose:1.8.0'
    implementation 'androidx.navigation:navigation-compose:2.7.5'

    // Accompanist Navigation Material (Required for AppKit)
    implementation 'com.google.accompanist:accompanist-navigation-material:0.32.0'


    def glide_version = "4.13.2";
    implementation "com.github.bumptech.glide:glide:$glide_version"
    annotationProcessor "com.github.bumptech.glide:compiler:$glide_version"

    implementation 'com.android.volley:volley:1.2.1'
    implementation 'com.squareup.picasso:picasso:2.71828'

    implementation 'com.facebook.android:facebook-login:[8,9)'
    implementation 'com.google.android.gms:play-services-auth:21.3.0'

    implementation 'com.github.TecOrb-Developers:SmartAlertDialog:v1.0'
//    implementation 'com.splunk.mint:mint:4.4.0'
    implementation 'de.hdodenhof:circleimageview:3.1.0'

//    implementation 'com.google.android.gms:play-services-auth:18.1.0'
//    implementation 'com.google.android.gms:play-services-auth-api-phone:17.5.0'

    // Razorpay
    implementation 'com.razorpay:checkout:1.6.41'

    // CASHFREE libraries
    implementation 'com.cashfree.pg:android-sdk:1.7.27'


    // Paytm libraries
    implementation 'com.paytm.appinvokesdk:appinvokesdk:1.6.8'

    //Payu Money
    implementation 'in.payu:payu-checkout-pro:2.4.5'


    implementation 'com.nhaarman.supertooltips:library:3.0.+'
    implementation 'com.eftimoff:android-pathview:1.0.8@aar'

    // implementation 'com.google.firebase:firebase-messaging:24.1.0'
    // Import the BoM for the Firebase platform
    // implementation platform('com.google.firebase:firebase-bom:33.7.0')
  //  implementation 'com.google.firebase:firebase-core'
//    implementation('com.google.firebase:firebase-auth') {
//        exclude module: "play-services-safetynet"
//    }
    // Declare the dependency for the Analytics library
    // When using the BoM, you don't specify versions in Firebase library dependencies
    // implementation 'com.google.firebase:firebase-crashlytics'
    // implementation 'com.google.firebase:firebase-analytics'


    // Google Place and location api
    implementation 'com.google.android.gms:play-services-location:21.3.0'
    implementation 'com.google.android.gms:play-services-places:17.1.0'


    implementation 'io.reactivex.rxjava2:rxandroid:2.1.1'
//    implementation 'io.reactivex.rxjava2:rxjava:2.2.16'

    implementation  "com.squareup.retrofit2:retrofit:$retrofitVersion"
    implementation  "com.squareup.retrofit2:converter-gson:$gsonConverterVersion"
    implementation  "com.squareup.okhttp3:okhttp:$okhttpVersion"
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
    implementation 'com.jakewharton:butterknife:10.2.3'
    annotationProcessor 'com.jakewharton:butterknife-compiler:10.2.3'
    implementation 'androidx.work:work-runtime:2.10.0'
    implementation 'com.google.code.gson:gson:2.11.0'
    implementation 'dev.shreyaspatil.EasyUpiPayment:EasyUpiPayment:3.0.3'

    implementation 'pl.droidsonroids.gif:android-gif-drawable:1.2.29'
    implementation 'pl.droidsonroids.gif:android-gif-drawable:1.2.29'
    implementation ('io.socket:socket.io-client:2.1.1') {
        exclude group: 'org.json', module: 'json'
    }
//slider
   // implementation 'com.github.devlight:infinitecycleviewpager:1.0.2'
    implementation('cn.trinea.android.view.autoscrollviewpager:android-auto-scroll-view-pager:1.1.2') {
        exclude module: 'support-v4'
    }
    implementation 'fr.avianey.com.viewpagerindicator:library:*******@aar'

    implementation 'com.github.bumptech.glide:glide:4.12.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.12.0'




}
// apply plugin: 'com.google.firebase.crashlytics'
// apply plugin: 'com.google.gms.google-services'

